{"name": "accessibility-analyzer", "version": "1.0.1", "description": "A comprehensive web application for analyzing and improving website accessibility compliance with WCAG guidelines.", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "build": "echo 'Using pre-built frontend'", "postinstall": "cd backend && npm install --production", "setup:firebase": "node backend/utils/firebase-setup.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"axe-core": "^4.10.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "playwright": "^1.53.1"}, "devDependencies": {"nodemon": "^3.1.10", "prettier": "^3.5.3"}}