# Simple Dockerfile for Railway deployment
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/

# Install dependencies with verbose logging
RUN npm install --verbose
RUN cd frontend && npm install --verbose
RUN cd ../backend && npm install --verbose

# Copy source code
COPY . .

# Build frontend with maximum compatibility settings
RUN cd frontend && \
    CI=false \
    NODE_OPTIONS="--max-old-space-size=4096" \
    GENERATE_SOURCEMAP=false \
    npm run build

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "start"]
