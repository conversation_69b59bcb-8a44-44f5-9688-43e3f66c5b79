# ACCESSI<PERSON><PERSON>ITY ANALYZER - COMPREHENSIVE PROJECT DOCUMENTATION
# From Zero to Advanced: Complete Technical Guide
# By <PERSON>me<PERSON> Tiwari

================================================================================
TABLE OF CONTENTS
================================================================================

1. PROJECT OVERVIEW & INTRODUCTION
2. ARCHITECTURE & TECHNOLOGY STACK
3. PROJECT STRUCTURE & FILE ORGANIZATION
4. BACKEND DEEP DIVE
5. FRONTEND DEEP DIVE
6. DATABASE DESIGN & FIRESTORE INTEGRATION
7. AUTHENTICATION SYSTEM
8. ACCESSIBI<PERSON>ITY SCANNING ENGINE
9. API DOCUMENTATION
10. DEVELOPMENT WORKFLOW
11. DEPLOYMENT & DEVOPS
12. SECURITY & PERFORMANCE
13. TESTING STRATEGY
14. TROUBLESHOOTING & COMMON ISSUES
15. FUTURE ENHANCEMENTS & ROADMAP

================================================================================
1. PROJECT OVERVIEW & INTRODUCTION
================================================================================

## What is the Accessibility Analyzer?

The Accessibility Analyzer is a comprehensive, full-stack web application designed to help developers, designers, and content creators ensure their websites meet accessibility standards and comply with Web Content Accessibility Guidelines (WCAG). Built by Janmejay Tiwari, this project represents a complete solution for automated accessibility testing and reporting.

## Core Mission

The primary mission of this project is to make web accessibility testing accessible to everyone, regardless of their technical expertise. It bridges the gap between complex accessibility guidelines and practical implementation by providing:

- Automated accessibility scanning using industry-standard tools
- Clear, actionable reports with specific recommendations
- User-friendly interface that doesn't require technical expertise
- Historical tracking to monitor accessibility improvements over time
- Educational resources to help users understand accessibility principles

## Key Features & Capabilities

### 1. Automated Accessibility Scanning
- **Engine**: Powered by axe-core, the industry-standard accessibility testing engine
- **Browser Automation**: Uses Playwright for reliable, headless browser testing
- **Comprehensive Coverage**: Tests against WCAG 2.0, 2.1, and 2.2 guidelines
- **Multiple Compliance Levels**: Supports Level A, AA, and AAA compliance testing
- **Real-time Analysis**: Provides immediate feedback on accessibility issues

### 2. Intelligent Reporting System
- **Detailed Issue Reports**: Each violation includes description, impact level, and fix recommendations
- **Severity Classification**: Issues categorized as Critical, Serious, Moderate, or Minor
- **Visual Documentation**: Screenshots and element highlighting for better understanding
- **Compliance Scoring**: Numerical scores to track accessibility improvements
- **Export Capabilities**: Reports can be exported in multiple formats

### 3. User Management & Authentication
- **Firebase Authentication**: Secure user management with Google OAuth integration
- **User Profiles**: Personalized dashboards and analysis history
- **Anonymous Usage**: Allows testing without registration for quick assessments
- **Role-based Access**: Different permission levels for different user types

### 4. Dashboard & Analytics
- **Progress Tracking**: Monitor accessibility improvements over time
- **Historical Comparisons**: Compare current results with previous scans
- **Trend Analysis**: Identify patterns in accessibility compliance
- **Visual Charts**: Graphical representation of accessibility metrics

### 5. Advanced Technical Features
- **Fallback Mechanisms**: Multiple scanning strategies for different website types
- **CSP Handling**: Advanced techniques to bypass Content Security Policy restrictions
- **Site-specific Optimizations**: Special handling for complex sites like YouTube, LinkedIn
- **Error Recovery**: Robust error handling and graceful degradation

## Target Audience

### Primary Users
1. **Web Developers**: Need to ensure their code meets accessibility standards
2. **UX/UI Designers**: Want to create inclusive design experiences
3. **Content Creators**: Need to verify their content is accessible
4. **QA Testers**: Require automated accessibility testing in their workflow
5. **Compliance Officers**: Need to verify organizational accessibility compliance

### Secondary Users
1. **Small Business Owners**: Want to make their websites accessible
2. **Educational Institutions**: Need to comply with accessibility regulations
3. **Government Agencies**: Required to meet accessibility standards
4. **Non-profit Organizations**: Want to ensure inclusive digital experiences

## Business Value & Impact

### For Organizations
- **Legal Compliance**: Helps meet ADA and Section 508 requirements
- **Risk Mitigation**: Reduces legal risks associated with inaccessible websites
- **Brand Reputation**: Demonstrates commitment to inclusivity and social responsibility
- **Market Expansion**: Makes products accessible to users with disabilities
- **Cost Efficiency**: Automated testing reduces manual testing costs

### For Developers
- **Skill Development**: Helps developers learn accessibility best practices
- **Quality Assurance**: Integrates accessibility testing into development workflow
- **Time Savings**: Automated scanning is faster than manual accessibility audits
- **Learning Tool**: Educational aspects help improve accessibility knowledge

## Technical Innovation

### Advanced Scanning Techniques
The project implements several innovative approaches to accessibility scanning:

1. **Multi-Strategy Injection**: Uses multiple methods to inject axe-core into websites, overcoming CSP restrictions
2. **Site-Specific Optimization**: Tailored loading strategies for different types of websites
3. **Fallback Analysis**: When axe-core fails, performs basic accessibility analysis
4. **Real-time Processing**: Asynchronous scanning with real-time status updates

### Scalable Architecture
- **Microservices Approach**: Modular backend architecture for easy scaling
- **Database Optimization**: Efficient Firestore queries with proper indexing
- **Caching Strategies**: Intelligent caching to improve performance
- **Load Balancing**: Designed to handle multiple concurrent scans

================================================================================
2. ARCHITECTURE & TECHNOLOGY STACK
================================================================================

## High-Level Architecture

The Accessibility Analyzer follows a modern, scalable architecture pattern that separates concerns and enables independent scaling of different components.

### Architecture Pattern: Three-Tier Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    PRESENTATION TIER                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   React Web     │  │   Mobile App    │  │   Admin Panel   │ │
│  │   Application   │  │   (Future)      │  │   (Future)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                     APPLICATION TIER                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Express.js    │  │   Authentication│  │   Scanning      │ │
│  │   API Server    │  │   Service       │  │   Service       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      DATA TIER                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Firebase      │  │   File Storage  │  │   Cache Layer   │ │
│  │   Firestore     │  │   (Screenshots) │  │   (Future)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Component Interaction Flow

```
User Request → React Frontend → Express API → Firebase Auth → 
Firestore Database → Scanning Service → Playwright Browser → 
axe-core Engine → Results Processing → Database Storage → 
API Response → Frontend Update → User Interface
```

## Technology Stack Deep Dive

### Backend Technologies

#### 1. Node.js Runtime Environment
- **Version**: 18.x LTS (Long Term Support)
- **Why Chosen**: 
  - Excellent performance for I/O intensive operations
  - Large ecosystem of packages
  - JavaScript consistency across frontend and backend
  - Strong community support and documentation
  - Native support for modern JavaScript features

#### 2. Express.js Web Framework
- **Version**: 4.18.2
- **Purpose**: RESTful API server and middleware management
- **Key Features Used**:
  - Route handling and middleware pipeline
  - JSON parsing and request validation
  - CORS configuration for cross-origin requests
  - Error handling and logging
  - Static file serving for production builds

#### 3. Firebase Admin SDK
- **Version**: 13.4.0
- **Purpose**: Backend integration with Firebase services
- **Services Used**:
  - **Firestore**: NoSQL document database
  - **Authentication**: User management and token verification
  - **Security Rules**: Database access control
- **Benefits**:
  - Real-time data synchronization
  - Automatic scaling
  - Built-in security features
  - Offline support capabilities

#### 4. Playwright Browser Automation
- **Version**: 1.53.1
- **Purpose**: Headless browser automation for web scraping and testing
- **Why Chosen Over Alternatives**:
  - Better performance than Selenium
  - More reliable than Puppeteer
  - Cross-browser support (Chromium, Firefox, Safari)
  - Advanced network interception capabilities
  - Better handling of modern web applications

#### 5. axe-core Accessibility Engine
- **Version**: 4.10.3
- **Purpose**: Industry-standard accessibility testing engine
- **Capabilities**:
  - WCAG 2.0, 2.1, and 2.2 compliance testing
  - Over 90 accessibility rules
  - Detailed violation reporting
  - Element-level issue identification
  - Severity classification

### Frontend Technologies

#### 1. React.js Framework
- **Version**: 18.2.0
- **Architecture Pattern**: Component-based architecture with hooks
- **Key Features Used**:
  - **Functional Components**: Modern React development approach
  - **React Hooks**: useState, useEffect, useContext, custom hooks
  - **Context API**: Global state management for authentication
  - **React Router**: Client-side routing and navigation
  - **Error Boundaries**: Graceful error handling

#### 2. Tailwind CSS Framework
- **Version**: 3.4.17
- **Purpose**: Utility-first CSS framework for rapid UI development
- **Benefits**:
  - Consistent design system
  - Responsive design utilities
  - Dark mode support
  - Custom component creation
  - Optimized bundle size through purging

#### 3. Headless UI Components
- **Version**: 2.2.4
- **Purpose**: Unstyled, accessible UI components
- **Components Used**:
  - Modal dialogs
  - Dropdown menus
  - Toggle switches
  - Form controls
- **Benefits**:
  - Built-in accessibility features
  - Keyboard navigation support
  - Screen reader compatibility

#### 4. Heroicons
- **Version**: 2.2.0
- **Purpose**: Beautiful, hand-crafted SVG icons
- **Usage**: Consistent iconography throughout the application

### Database & Storage

#### Firebase Firestore
- **Type**: NoSQL Document Database
- **Structure**: Collections and Documents
- **Key Collections**:
  - `users`: User profile information
  - `analysisRequests`: Scan requests and metadata
  - `analysisResults`: Detailed scan results and reports
  - `userPreferences`: User settings and preferences

#### Security Rules
- **Purpose**: Database access control and data validation
- **Features**:
  - User-based access control
  - Data validation rules
  - Public/private data separation
  - Anonymous user support

### Development & Build Tools

#### 1. Create React App
- **Purpose**: React application scaffolding and build system
- **Features**:
  - Hot reloading during development
  - Production build optimization
  - Built-in testing framework
  - ESLint integration

#### 2. PostCSS & Autoprefixer
- **Purpose**: CSS processing and vendor prefix automation
- **Benefits**:
  - Cross-browser compatibility
  - CSS optimization
  - Future CSS feature support

#### 3. Jest Testing Framework
- **Purpose**: Unit and integration testing
- **Features**:
  - Component testing
  - API endpoint testing
  - Mock implementations
  - Coverage reporting

### DevOps & Deployment

#### 1. Docker Containerization
- **Base Image**: node:18-alpine
- **Benefits**:
  - Consistent deployment environment
  - Easy scaling and orchestration
  - Isolation from host system
  - Simplified dependency management

#### 2. Railway Platform
- **Purpose**: Cloud deployment and hosting
- **Features**:
  - Automatic deployments from Git
  - Environment variable management
  - Built-in monitoring and logging
  - Scalable infrastructure

#### 3. Vercel (Frontend Alternative)
- **Purpose**: Frontend deployment and CDN
- **Benefits**:
  - Global edge network
  - Automatic HTTPS
  - Preview deployments
  - Performance optimization

## Design Patterns & Principles

### Backend Design Patterns

#### 1. MVC (Model-View-Controller) Pattern
- **Models**: Data structures and database interactions
- **Views**: JSON API responses
- **Controllers**: Business logic and request handling

#### 2. Repository Pattern
- **Purpose**: Abstraction layer for data access
- **Implementation**: Model classes with static methods
- **Benefits**: Easier testing and database switching

#### 3. Middleware Pattern
- **Purpose**: Request/response processing pipeline
- **Examples**: Authentication, logging, error handling
- **Benefits**: Separation of concerns and reusability

### Frontend Design Patterns

#### 1. Component Composition Pattern
- **Purpose**: Building complex UIs from simple components
- **Implementation**: Reusable, composable React components
- **Benefits**: Code reusability and maintainability

#### 2. Provider Pattern
- **Purpose**: Global state management
- **Implementation**: React Context API
- **Usage**: Authentication state, theme management

#### 3. Custom Hooks Pattern
- **Purpose**: Logic reuse across components
- **Examples**: useAuth, useApi, useLocalStorage
- **Benefits**: Cleaner components and better testing

### Security Principles

#### 1. Defense in Depth
- **Multiple Security Layers**: Authentication, authorization, input validation
- **Implementation**: Firebase Auth + Firestore Rules + API validation

#### 2. Principle of Least Privilege
- **User Permissions**: Users can only access their own data
- **API Access**: Endpoints require appropriate authentication

#### 3. Input Validation & Sanitization
- **URL Validation**: Comprehensive URL format checking
- **Data Sanitization**: Preventing XSS and injection attacks
- **Rate Limiting**: Preventing abuse and DoS attacks

================================================================================
3. PROJECT STRUCTURE & FILE ORGANIZATION
================================================================================

## Root Directory Structure

```
accessibility-analyzer/
├── backend/                    # Node.js/Express backend server
├── frontend/                   # React.js frontend application
├── scripts/                    # Utility and setup scripts
├── node_modules/              # Root-level dependencies
├── .env                       # Environment variables (not in repo)
├── .gitignore                 # Git ignore rules
├── docker-compose.yml         # Docker composition configuration
├── Dockerfile                 # Docker container configuration
├── firebase.json              # Firebase project configuration
├── firestore.indexes.json     # Firestore database indexes
├── firestore.rules           # Firestore security rules
├── package.json              # Root package configuration
├── package-lock.json         # Dependency lock file
├── README.md                 # Project documentation
└── [deployment files]        # Various deployment guides and configs
```

## Backend Directory Deep Dive

### Backend Structure
```
backend/
├── config/                    # Configuration files
│   ├── firebase-admin.js     # Firebase Admin SDK setup
│   ├── firebase-service-account-key.json  # Service account credentials
│   └── README.md             # Configuration documentation
├── controllers/              # Request handlers and business logic
│   ├── analysisController.js # Analysis-related endpoints
│   └── authController.js     # Authentication endpoints
├── middleware/               # Express middleware functions
│   └── [middleware files]    # Authentication, validation, etc.
├── models/                   # Data models and database interactions
│   ├── AnalysisRequest.js    # Analysis request model
│   └── AnalysisResult.js     # Analysis result model
├── routes/                   # API route definitions
│   ├── analysis.js          # Analysis-related routes
│   ├── auth.js              # Authentication routes
│   └── index.js             # Route aggregation
├── utils/                    # Utility functions and services
│   ├── accessibilityScanner.js  # Core scanning engine
│   ├── scanningService.js    # Scanning orchestration
│   ├── validation.js        # Input validation utilities
│   ├── dataIntegrityService.js  # Data consistency checks
│   └── firebase-setup.js    # Firebase initialization script
├── node_modules/            # Backend dependencies
├── package.json            # Backend package configuration
├── package-lock.json       # Backend dependency lock
└── server.js              # Main server entry point
```

### Backend File Purposes

#### Configuration Files
- **firebase-admin.js**: Initializes Firebase Admin SDK, handles authentication
- **firebase-service-account-key.json**: Contains Firebase service account credentials (sensitive)

#### Controllers
- **analysisController.js**: Handles all analysis-related API endpoints
  - Create analysis requests
  - Retrieve analysis results
  - Manage scan status
  - Generate analytics data
  - Handle historical comparisons

- **authController.js**: Manages authentication-related operations
  - User profile management
  - Token verification
  - User preferences

#### Models
- **AnalysisRequest.js**: Database model for analysis requests
  - CRUD operations for analysis requests
  - User-specific queries
  - Status management
  - Fallback to in-memory storage

- **AnalysisResult.js**: Database model for analysis results
  - Store scan results
  - Generate analytics
  - Historical data management

#### Routes
- **analysis.js**: Defines analysis-related API endpoints
- **auth.js**: Defines authentication API endpoints
- **index.js**: Aggregates all routes and provides API information

#### Utilities
- **accessibilityScanner.js**: Core accessibility scanning engine
  - Playwright browser automation
  - axe-core integration
  - Multiple injection strategies
  - Site-specific optimizations
  - Fallback analysis methods

- **scanningService.js**: Orchestrates the scanning process
  - Manages scan queue
  - Handles concurrent scans
  - Provides status updates
  - Error handling and recovery

- **validation.js**: Input validation and sanitization
  - URL validation
  - Settings validation
  - Pagination validation
  - Security checks

## Frontend Directory Deep Dive

### Frontend Structure
```
frontend/
├── public/                   # Static assets
│   ├── index.html           # Main HTML template
│   ├── favicon.ico          # Site favicon
│   ├── manifest.json        # PWA manifest
│   └── robots.txt           # Search engine instructions
├── src/                     # Source code
│   ├── components/          # Reusable UI components
│   │   ├── analysis/        # Analysis-specific components
│   │   ├── auth/           # Authentication components
│   │   ├── common/         # Shared components
│   │   └── layout/         # Layout components
│   ├── contexts/           # React Context providers
│   │   └── AuthContext.js  # Authentication context
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API and external service integrations
│   │   ├── api/           # API service layer
│   │   └── firebase/      # Firebase client services
│   ├── utils/             # Utility functions
│   ├── views/             # Page components
│   │   ├── Analysis/      # Analysis page
│   │   ├── Auth/          # Authentication page
│   │   ├── Dashboard/     # User dashboard
│   │   ├── Home/          # Landing page
│   │   ├── Login/         # Login page
│   │   └── Profile/       # User profile page
│   ├── __tests__/         # Test files
│   ├── App.js             # Main application component
│   ├── App.css            # Application styles
│   ├── index.js           # Application entry point
│   └── index.css          # Global styles
├── build/                  # Production build output
├── node_modules/          # Frontend dependencies
├── package.json          # Frontend package configuration
├── tailwind.config.js    # Tailwind CSS configuration
├── postcss.config.js     # PostCSS configuration
└── vercel.json           # Vercel deployment configuration
```

### Frontend Component Architecture

#### Components Directory Structure
```
components/
├── analysis/              # Analysis-specific components
│   ├── AnalysisResults.js # Display scan results
│   ├── UrlInputForm.js    # URL input and validation
│   └── __tests__/         # Component tests
├── auth/                  # Authentication components
│   └── ForgotPassword.js  # Password reset functionality
├── common/                # Shared/reusable components
│   ├── BrandLogo.js       # Application logo
│   ├── ErrorBoundary.js   # Error handling wrapper
│   ├── ErrorMessage.js    # Error display component
│   ├── Footer.js          # Site footer
│   ├── Loading.js         # Loading indicators
│   ├── ProtectedRoute.js  # Route protection
│   ├── StatusIndicator.js # Status display
│   └── Toast.js           # Notification system
└── layout/                # Layout components
    └── Layout.js          # Main application layout
```

#### Views Directory Structure
```
views/
├── Analysis/              # Analysis page and related components
│   └── Analysis.js        # Main analysis page
├── AnalysisHistory/       # Analysis history page
│   └── AnalysisHistory.js # User's analysis history
├── Auth/                  # Authentication page
│   └── Auth.js            # Authentication form
├── Dashboard/             # User dashboard
│   └── Dashboard.js       # Analytics and overview
├── Home/                  # Landing page
│   └── Home.js            # Main landing page
├── Login/                 # Login page
│   ├── Login.js           # Login form and logic
│   └── Login.css          # Login-specific styles
└── Profile/               # User profile page
    └── Profile.js         # User profile management
```

#### Services Directory Structure
```
services/
├── api/                   # API service layer
│   ├── analysisService.js # Analysis API calls
│   ├── client.js          # HTTP client configuration
│   └── __tests__/         # API service tests
└── firebase/              # Firebase client services
    └── authService.js     # Firebase authentication
```

## Configuration Files Deep Dive

### Root Configuration Files

#### package.json (Root)
```json
{
  "name": "accessibility-analyzer",
  "version": "1.0.1",
  "description": "A comprehensive web application for analyzing and improving website accessibility compliance with WCAG guidelines.",
  "main": "backend/server.js",
  "scripts": {
    "start": "node backend/server.js",
    "dev": "nodemon backend/server.js",
    "build": "echo 'Using pre-built frontend'",
    "postinstall": "cd backend && npm install --production",
    "setup:firebase": "node backend/utils/firebase-setup.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  },
  "dependencies": {
    "axe-core": "^4.10.3",
    "cors": "^2.8.5",
    "dotenv": "^16.5.0",
    "express": "^4.18.2",
    "firebase-admin": "^13.4.0",
    "helmet": "^8.1.0",
    "morgan": "^1.10.0",
    "playwright": "^1.53.1"
  },
  "devDependencies": {
    "nodemon": "^3.1.10",
    "prettier": "^3.5.3"
  }
}
```

#### firebase.json
```json
{
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "emulators": {
    "auth": {
      "port": 9099
    },
    "firestore": {
      "port": 8080
    },
    "ui": {
      "enabled": true,
      "port": 4000
    },
    "singleProjectMode": true
  }
}
```

#### Dockerfile
```dockerfile
# Use Node.js 18 LTS
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy everything first
COPY . .

# Install root dependencies
RUN npm install --production

# Install backend dependencies
RUN cd backend && npm install --production

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "start"]
```

### Environment Configuration

#### Environment Variables Structure
```
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY=your-private-key
FIREBASE_CLIENT_EMAIL=your-client-email

# Application Configuration
NODE_ENV=development|production
PORT=3000
FRONTEND_URL=http://localhost:3001

# Railway Deployment (Production)
RAILWAY_STATIC_URL=your-railway-url
PUBLIC_IP=your-public-ip

# API Configuration
API_TIMEOUT=60000
MAX_CONCURRENT_SCANS=5
```

## File Naming Conventions

### Backend Conventions
- **Controllers**: `[entity]Controller.js` (e.g., `analysisController.js`)
- **Models**: `[Entity].js` (e.g., `AnalysisRequest.js`)
- **Routes**: `[entity].js` (e.g., `analysis.js`)
- **Utilities**: `[purpose]Service.js` or `[purpose].js`
- **Configuration**: `[service]-[purpose].js` (e.g., `firebase-admin.js`)

### Frontend Conventions
- **Components**: `[ComponentName].js` (PascalCase)
- **Views/Pages**: `[PageName].js` (PascalCase)
- **Services**: `[service]Service.js` (camelCase + Service suffix)
- **Hooks**: `use[HookName].js` (camelCase with 'use' prefix)
- **Contexts**: `[Context]Context.js` (PascalCase + Context suffix)
- **Utilities**: `[purpose].js` (camelCase)

### Test File Conventions
- **Unit Tests**: `[ComponentName].test.js`
- **Integration Tests**: `[Feature].integration.test.js`
- **Test Directories**: `__tests__/` (within each module)

## Import/Export Patterns

### Backend Module Patterns
```javascript
// Named exports for utilities
module.exports = {
  functionName,
  anotherFunction
};

// Default export for classes
class ClassName {
  // class implementation
}
module.exports = ClassName;

// Mixed exports
const utilityFunction = () => {};
class MainClass {}
module.exports = MainClass;
module.exports.utilityFunction = utilityFunction;
```

### Frontend Module Patterns
```javascript
// Default export for components
const ComponentName = () => {
  // component implementation
};
export default ComponentName;

// Named exports for services
export const serviceName = {
  method1,
  method2
};

// Mixed exports
export const utilityFunction = () => {};
const MainComponent = () => {};
export default MainComponent;
```

## Code Organization Principles

### 1. Separation of Concerns
- **Business Logic**: Separated from presentation logic
- **Data Access**: Isolated in model classes
- **API Logic**: Contained in service classes
- **UI Logic**: Contained in components

### 2. Single Responsibility Principle
- **Each File**: Has one primary purpose
- **Each Function**: Performs one specific task
- **Each Component**: Handles one UI concern

### 3. Dependency Injection
- **Configuration**: Injected through environment variables
- **Services**: Passed as parameters or context
- **Database**: Abstracted through model classes

### 4. Modular Architecture
- **Feature-based**: Grouping by functionality
- **Reusable Components**: Shared across features
- **Utility Functions**: Centralized and reusable

This completes the first major section of the documentation. The project structure is organized following modern best practices with clear separation of concerns, modular architecture, and consistent naming conventions. Each file and directory has a specific purpose and follows established patterns for maintainability and scalability.

================================================================================
4. BACKEND DEEP DIVE
================================================================================

## Server Architecture & Entry Point

### Main Server File (server.js)

The server.js file serves as the application's entry point and orchestrates the entire backend system. It follows a layered architecture pattern with clear separation of concerns.

#### Server Initialization Process

```javascript
// 1. Environment Setup
require('dotenv').config();

// 2. Firebase Initialization
const { initializeFirebase } = require('./config/firebase-admin');
try {
  initializeFirebase();
} catch (error) {
  console.error('Failed to initialize Firebase:', error);
  process.exit(1);
}

// 3. Express App Configuration
const app = express();
const PORT = process.env.PORT || 3000;

// 4. Security Middleware
app.use(helmet({
  contentSecurityPolicy: false,
  crossOriginEmbedderPolicy: false
}));

// 5. CORS Configuration
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? [process.env.FRONTEND_URL, process.env.RAILWAY_STATIC_URL]
    : ['http://localhost:3001', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

// 6. Request Processing Middleware
app.use(cors(corsOptions));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 7. Static File Serving (Production)
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../frontend/build')));
}

// 8. API Routes
app.use('/api', require('./routes/index'));

// 9. Error Handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'production' ? {} : err.message
  });
});

// 10. Server Startup
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Firebase Admin SDK initialized successfully`);
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
});
```

#### Key Server Features

1. **Environment-Aware Configuration**: Different settings for development and production
2. **Security Headers**: Helmet.js for security best practices
3. **CORS Management**: Flexible cross-origin resource sharing
4. **Request Logging**: Morgan for comprehensive request logging
5. **Error Handling**: Centralized error management with environment-specific responses
6. **Static File Serving**: Serves React build in production
7. **Health Checks**: Built-in health monitoring endpoints

### Firebase Configuration & Integration

#### Firebase Admin SDK Setup (config/firebase-admin.js)

```javascript
const admin = require('firebase-admin');
const path = require('path');

let db = null;

const initializeFirebase = () => {
  try {
    // Check if already initialized
    if (admin.apps.length > 0) {
      console.log('✅ Firebase Admin already initialized');
      db = admin.firestore();
      return;
    }

    // Service Account Key Path
    const serviceAccountPath = path.join(__dirname, 'firebase-service-account-key.json');

    // Initialize with service account
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccountPath),
      projectId: process.env.FIREBASE_PROJECT_ID
    });

    // Initialize Firestore
    db = admin.firestore();

    // Configure Firestore settings
    db.settings({
      timestampsInSnapshots: true,
      ignoreUndefinedProperties: true
    });

    console.log('✅ Firebase Admin SDK initialized successfully');
  } catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    throw error;
  }
};

const getFirestore = () => {
  if (!db) {
    throw new Error('Firestore not initialized. Call initializeFirebase() first.');
  }
  return db;
};

const getAuth = () => {
  return admin.auth();
};

module.exports = {
  initializeFirebase,
  getFirestore,
  getAuth,
  admin
};
```

#### Firebase Integration Benefits

1. **Real-time Database**: Instant data synchronization
2. **Authentication**: Secure user management
3. **Security Rules**: Database-level access control
4. **Scalability**: Automatic scaling based on usage
5. **Offline Support**: Built-in offline capabilities
6. **Multi-platform**: Consistent across web, mobile, and server

## API Architecture & Route Management

### Route Organization Strategy

The API follows RESTful principles with logical resource grouping:

```
/api/
├── /auth/                 # Authentication endpoints
│   ├── POST /verify       # Verify Firebase token
│   ├── GET /profile       # Get user profile
│   └── PUT /profile       # Update user profile
├── /analysis/             # Analysis endpoints
│   ├── POST /             # Create analysis request
│   ├── GET /:id           # Get analysis request
│   ├── GET /:id/result    # Get analysis result
│   ├── GET /:id/status    # Get scan status
│   ├── POST /:id/scan     # Trigger manual scan
│   ├── GET /user/requests # Get user's analyses
│   ├── GET /user/results  # Get user's results
│   ├── GET /public/recent # Get recent public analyses
│   └── GET /dashboard/analytics # Get analytics data
└── /                      # API information
```

### Route Implementation Pattern

#### Main Route Index (routes/index.js)

```javascript
const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./auth');
const analysisRoutes = require('./analysis');

// Mount routes with prefixes
router.use('/auth', authRoutes);
router.use('/analysis', analysisRoutes);

// API information endpoint
router.get('/', (req, res) => {
  res.json({
    message: 'Accessibility Analyzer API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      analysis: '/api/analysis'
    },
    documentation: '/api/docs'
  });
});

module.exports = router;
```

#### Analysis Routes (routes/analysis.js)

```javascript
const express = require('express');
const router = express.Router();
const analysisController = require('../controllers/analysisController');
const authMiddleware = require('../middleware/auth');

// Public endpoints (no authentication required)
router.post('/', analysisController.createAnalysisRequest);
router.get('/:id', analysisController.getAnalysisRequest);
router.get('/:id/result', analysisController.getAnalysisResult);
router.get('/:id/status', analysisController.getScanStatus);
router.get('/public/recent', analysisController.getRecentAnalyses);

// Protected endpoints (authentication required)
router.get('/user/requests', authMiddleware.verifyToken, analysisController.getUserAnalysisRequests);
router.get('/user/results', authMiddleware.verifyToken, analysisController.getUserAnalysisResults);
router.get('/dashboard/analytics', authMiddleware.verifyToken, analysisController.getAnalytics);

// Analysis management endpoints
router.post('/:id/scan', authMiddleware.optionalAuth, analysisController.triggerScan);
router.delete('/:id/scan', authMiddleware.optionalAuth, analysisController.cancelScan);

// Advanced analysis endpoints
router.get('/:id/violations', analysisController.getViolationAnalysis);
router.get('/history/comparison', analysisController.getHistoricalComparison);

module.exports = router;
```

## Controller Architecture & Business Logic

### Analysis Controller Deep Dive

The analysisController.js file contains the core business logic for all analysis-related operations. It follows the controller pattern with clear separation of concerns.

#### Key Controller Methods

##### 1. Create Analysis Request
```javascript
const createAnalysisRequest = async (req, res) => {
  try {
    const { url, settings } = req.body;

    // Input validation
    const urlValidation = validateUrl(url);
    if (!urlValidation.isValid) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'URL validation failed',
        details: urlValidation.errors
      });
    }

    // Create analysis request in database
    const analysisRequest = await AnalysisRequest.create({
      url: urlValidation.normalizedUrl,
      userId: req.user?.uid || null,
      settings: settingsValidation.settings,
      status: 'pending'
    });

    // Trigger asynchronous scanning
    setImmediate(async () => {
      try {
        await scanningService.processAnalysisRequest(analysisRequest.id);
      } catch (error) {
        console.error(`Background scan failed for request ${analysisRequest.id}:`, error);
      }
    });

    res.status(201).json({
      message: 'Analysis request created successfully. Scanning will begin shortly.',
      data: analysisRequest
    });
  } catch (error) {
    console.error('Error creating analysis request:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create analysis request'
    });
  }
};
```

##### 2. Get Analysis Results
```javascript
const getAnalysisResult = async (req, res) => {
  try {
    const { id } = req.params;

    // Check permissions
    const analysisRequest = await AnalysisRequest.getById(id);
    if (!analysisRequest) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Analysis request not found'
      });
    }

    // Verify user permissions
    if (analysisRequest.userId && req.user?.uid !== analysisRequest.userId) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You do not have permission to view this analysis result'
      });
    }

    // Get analysis result
    const analysisResult = await AnalysisResult.getByAnalysisRequestId(id);
    if (!analysisResult) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Analysis result not found'
      });
    }

    res.json({
      message: 'Analysis result retrieved successfully',
      data: analysisResult
    });
  } catch (error) {
    console.error('Error getting analysis result:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve analysis result'
    });
  }
};
```

#### Controller Design Patterns

1. **Error Handling**: Consistent error response format
2. **Input Validation**: Comprehensive request validation
3. **Permission Checking**: User-based access control
4. **Async Processing**: Non-blocking operations for long-running tasks
5. **Response Standardization**: Consistent API response structure

### Data Models & Database Abstraction

#### Analysis Request Model (models/AnalysisRequest.js)

The AnalysisRequest model provides an abstraction layer for database operations with fallback mechanisms.

```javascript
class AnalysisRequest {
  constructor(data) {
    this.url = data.url;
    this.userId = data.userId || null;
    this.status = data.status || 'pending';
    this.requestTimestamp = data.requestTimestamp || new Date();
    this.completedTimestamp = data.completedTimestamp || null;
    this.metadata = data.metadata || {};
    this.settings = data.settings || {
      includeAxeCore: true,
      includeMlAnalysis: false,
      wcagLevel: 'AA'
    };
  }

  // Create new analysis request with fallback
  static async create(data) {
    try {
      // Try Firebase first
      const db = getFirestore();
      const analysisRequest = new AnalysisRequest(data);
      const docRef = await db.collection('analysisRequests').add({
        ...analysisRequest,
        requestTimestamp: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      });

      return { id: docRef.id, ...analysisRequest };
    } catch (firebaseError) {
      // Fallback to in-memory storage
      console.warn('⚠️ Firebase unavailable, using in-memory storage');
      const analysisRequest = new AnalysisRequest(data);
      const id = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const requestData = { id, ...analysisRequest };
      inMemoryStorage.set(id, requestData);
      return requestData;
    }
  }

  // Get by ID with fallback
  static async getById(id) {
    try {
      // Check in-memory storage for memory-based IDs
      if (id.startsWith('mem_') && inMemoryStorage.has(id)) {
        return inMemoryStorage.get(id);
      }

      // Try Firebase
      const db = getFirestore();
      const doc = await db.collection('analysisRequests').doc(id).get();
      if (!doc.exists) return null;
      return { id: doc.id, ...doc.data() };
    } catch (firebaseError) {
      return inMemoryStorage.get(id) || null;
    }
  }

  // Additional methods for querying, updating, deleting...
}
```

#### Model Design Features

1. **Fallback Mechanism**: In-memory storage when Firebase is unavailable
2. **Data Validation**: Built-in data structure validation
3. **Timestamp Management**: Automatic timestamp handling
4. **User Association**: Support for both authenticated and anonymous users
5. **Flexible Querying**: Support for various query patterns

## Utility Services & Core Functionality

### Accessibility Scanner Service

The accessibilityScanner.js file contains the core scanning engine that powers the entire application. It's a sophisticated system that handles various edge cases and provides robust accessibility analysis.

#### Scanner Architecture

```javascript
class AccessibilityScanner {
  constructor(options = {}) {
    this.options = {
      timeout: options.timeout || 45000,
      waitForSelector: options.waitForSelector || 'body',
      viewport: options.viewport || { width: 1280, height: 720 },
      userAgent: options.userAgent || 'Mozilla/5.0...',
      ...options
    };
    this.browser = null;
    this.page = null;
  }

  // Main scanning workflow
  async scan(url, options = {}) {
    try {
      await this.initialize();           // Launch browser
      const navigation = await this.navigateToUrl(url);  // Navigate to URL
      const metadata = await this.getPageMetadata();     // Extract metadata
      const scanResults = await this.runAccessibilityScan(options);  // Run scan
      const screenshot = options.captureScreenshot ?
        await this.captureScreenshot(options.screenshotOptions) : null;

      return {
        ...scanResults,
        navigation,
        metadata: { ...scanResults.metadata, page: metadata },
        screenshot: screenshot ? screenshot.toString('base64') : null
      };
    } finally {
      await this.cleanup();  // Always cleanup resources
    }
  }
}
```

#### Advanced Scanning Features

##### 1. Multi-Strategy axe-core Injection
```javascript
async runAccessibilityScan(options = {}) {
  try {
    // Strategy 1: Standard injection
    await this.page.addScriptTag({ content: axeCore.source });
  } catch (cspError) {
    try {
      // Strategy 2: Direct evaluation (bypasses some CSP)
      await this.page.evaluate((axeSource) => {
        const script = document.createElement('script');
        script.textContent = axeSource;
        document.head.appendChild(script);
      }, axeCore.source);
    } catch (fallbackError) {
      try {
        // Strategy 3: Iframe context (for strict CSP)
        await this.page.evaluate((axeSource) => {
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          document.body.appendChild(iframe);
          const iframeDoc = iframe.contentDocument;
          const script = iframeDoc.createElement('script');
          script.textContent = axeSource;
          iframeDoc.head.appendChild(script);
          if (iframe.contentWindow.axe) {
            window.axe = iframe.contentWindow.axe;
          }
        }, axeCore.source);
      } catch (iframeError) {
        // Strategy 4: Init script with page reload
        await this.page.addInitScript({ content: axeCore.source });
        await this.page.reload({ waitUntil: 'networkidle' });
      }
    }
  }

  // Run axe-core scan
  const results = await this.page.evaluate((axeOptions) => {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Accessibility scan timeout'));
      }, 30000);

      window.axe.run(document, axeOptions, (err, results) => {
        clearTimeout(timeout);
        if (err) reject(err);
        else resolve(results);
      });
    });
  }, axeOptions);

  return this.processAxeResults(results);
}
```

##### 2. Site-Specific Optimization
```javascript
async waitForPageReady(url) {
  const domain = new URL(url).hostname.toLowerCase();

  if (domain.includes('youtube.com')) {
    await this.waitForYouTube();
  } else if (domain.includes('linkedin.com')) {
    await this.waitForLinkedIn();
  } else {
    await this.waitForDefaultSite();
  }
}

async waitForYouTube() {
  const selectors = ['ytd-app', '#content', 'ytd-masthead', '[role="main"]'];

  for (const selector of selectors) {
    try {
      await this.page.waitForSelector(selector, {
        timeout: 10000,
        state: 'attached'
      });
      await this.page.waitForTimeout(2000);  // Stabilization time
      return;
    } catch (error) {
      continue;  // Try next selector
    }
  }
}
```

##### 3. Fallback Analysis System
```javascript
async performBasicAccessibilityAnalysis() {
  const basicResults = await this.page.evaluate(() => {
    const violations = [];
    const passes = [];

    // Check for missing alt text
    const images = document.querySelectorAll('img');
    let missingAltCount = 0;
    images.forEach(img => {
      if (!img.alt && !img.getAttribute('aria-label')) {
        missingAltCount++;
      }
    });

    if (missingAltCount > 0) {
      violations.push({
        id: 'image-alt',
        impact: 'serious',
        tags: ['wcag2a', 'wcag111'],
        description: 'Images must have alternate text',
        nodes: [{
          html: `Found ${missingAltCount} images without alt text`,
          failureSummary: `${missingAltCount} images are missing alternative text`
        }]
      });
    }

    // Check for page title
    if (!document.title || document.title.trim().length === 0) {
      violations.push({
        id: 'document-title',
        impact: 'serious',
        tags: ['wcag2a', 'wcag242'],
        description: 'Documents must have a title'
      });
    }

    // Additional basic checks...
    return { violations, passes, incomplete: [], inapplicable: [] };
  });

  return this.processAxeResults(basicResults);
}
```

### Scanning Service Orchestration

The scanningService.js manages the scanning workflow and provides status tracking.

```javascript
class ScanningService {
  constructor() {
    this.activeScanners = new Map();
    this.scanQueue = [];
    this.maxConcurrentScans = 3;
  }

  async processAnalysisRequest(analysisRequestId) {
    try {
      // Update status to processing
      await AnalysisRequest.update(analysisRequestId, {
        status: 'processing',
        metadata: { startTime: new Date() }
      });

      // Get analysis request details
      const analysisRequest = await AnalysisRequest.getById(analysisRequestId);
      if (!analysisRequest) {
        throw new Error('Analysis request not found');
      }

      // Create scanner instance
      const scanner = new AccessibilityScanner({
        timeout: 45000,
        viewport: { width: 1280, height: 720 }
      });

      // Track active scanner
      this.activeScanners.set(analysisRequestId, scanner);

      // Perform scan
      const scanResults = await scanner.scan(analysisRequest.url, {
        captureScreenshot: true,
        axeOptions: {
          runOnly: {
            type: 'tag',
            values: ['wcag2a', 'wcag2aa', 'wcag21aa', 'best-practice']
          }
        }
      });

      // Process and save results
      const analysisResult = await this.processScanResults(
        analysisRequestId,
        scanResults,
        analysisRequest
      );

      // Update request status
      await AnalysisRequest.update(analysisRequestId, {
        status: 'completed',
        completedTimestamp: new Date(),
        metadata: {
          ...analysisRequest.metadata,
          endTime: new Date(),
          scanDuration: Date.now() - new Date(analysisRequest.metadata.startTime).getTime()
        }
      });

      return analysisResult;
    } catch (error) {
      // Handle scan failure
      await this.handleScanFailure(analysisRequestId, error);
      throw error;
    } finally {
      // Cleanup
      this.activeScanners.delete(analysisRequestId);
    }
  }

  async processScanResults(analysisRequestId, scanResults, analysisRequest) {
    // Create comprehensive analysis result
    const analysisResult = {
      analysisRequestId,
      url: analysisRequest.url,
      userId: analysisRequest.userId,
      axeCoreResults: scanResults,
      summary: this.generateSummary(scanResults),
      recommendations: this.generateRecommendations(scanResults),
      complianceScore: this.calculateComplianceScore(scanResults),
      createdAt: new Date()
    };

    // Save to database
    const savedResult = await AnalysisResult.create(analysisResult);
    return savedResult;
  }

  generateSummary(scanResults) {
    return {
      totalViolations: scanResults.violations.length,
      totalPasses: scanResults.passes.length,
      totalIncomplete: scanResults.incomplete.length,
      criticalIssues: scanResults.violations.filter(v => v.impact === 'critical').length,
      seriousIssues: scanResults.violations.filter(v => v.impact === 'serious').length,
      moderateIssues: scanResults.violations.filter(v => v.impact === 'moderate').length,
      minorIssues: scanResults.violations.filter(v => v.impact === 'minor').length,
      complianceScore: this.calculateComplianceScore(scanResults)
    };
  }
}
```

This completes the Backend Deep Dive section, covering server architecture, API design, controller patterns, data models, and the sophisticated accessibility scanning engine that powers the application.

================================================================================
5. FRONTEND DEEP DIVE
================================================================================

## React Application Architecture

### Application Entry Point & Structure

The frontend follows a modern React architecture with functional components, hooks, and context-based state management. The application is structured around a component-based architecture that promotes reusability and maintainability.

#### Main Application Component (App.js)

```javascript
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ToastProvider } from './components/common/Toast';
import ErrorBoundary from './components/common/ErrorBoundary';
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/common/ProtectedRoute';

// Page Components
import Login from './views/Login/Login';
import Home from './views/Home/Home';
import Analysis from './views/Analysis/Analysis';
import Dashboard from './views/Dashboard/Dashboard';
import Profile from './views/Profile/Profile';
import Auth from './views/Auth/Auth';

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <ToastProvider>
          <Router>
            <div className="min-h-screen">
              <Routes>
                {/* Landing Page */}
                <Route path="/" element={<Login />} />

                {/* Main Application Routes */}
                <Route path="/*" element={
                  <Layout>
                    <Routes>
                      <Route path="/home" element={<Home />} />
                      <Route path="/analysis/:id" element={<Analysis />} />
                      <Route path="/dashboard" element={
                        <ProtectedRoute>
                          <Dashboard />
                        </ProtectedRoute>
                      } />
                      <Route path="/profile" element={
                        <ProtectedRoute>
                          <Profile />
                        </ProtectedRoute>
                      } />
                      <Route path="/auth" element={<Auth />} />
                    </Routes>
                  </Layout>
                } />
              </Routes>
            </div>
          </Router>
        </ToastProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
```

#### Application Architecture Patterns

1. **Provider Pattern**: Global state management through React Context
2. **Higher-Order Components**: Error boundaries and route protection
3. **Compound Components**: Layout components with nested routing
4. **Render Props**: Flexible component composition
5. **Custom Hooks**: Reusable stateful logic

### State Management Strategy

#### Authentication Context (contexts/AuthContext.js)

The AuthContext provides global authentication state management throughout the application.

```javascript
import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '../services/firebase/authService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Listen for auth state changes
    const unsubscribe = authService.onAuthStateChanged((user) => {
      setUser(user);
      setLoading(false);

      // Store auth token for API requests
      if (user) {
        user.getIdToken().then(token => {
          localStorage.setItem('authToken', token);
        });
      } else {
        localStorage.removeItem('authToken');
      }
    });

    return unsubscribe;
  }, []);

  const signUp = async (email, password, displayName) => {
    try {
      setError('');
      setLoading(true);
      const user = await authService.signUp(email, password, displayName);
      return user;
    } catch (error) {
      setError(getErrorMessage(error));
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    try {
      setError('');
      setLoading(true);
      const user = await authService.signInWithGoogle();
      return user;
    } catch (error) {
      setError(getErrorMessage(error));
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Additional authentication methods...

  const value = {
    user,
    loading,
    error,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    updateProfile,
    isAuthenticated: !!user,
    displayName: user?.displayName || 'User'
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

#### Context Design Benefits

1. **Global State**: Authentication state available throughout the app
2. **Automatic Token Management**: Handles token storage and refresh
3. **Error Handling**: Centralized authentication error management
4. **Loading States**: Provides loading indicators for auth operations
5. **Type Safety**: Custom hook ensures proper context usage

### Component Architecture & Design System

#### Layout Component System

The layout system provides consistent structure and navigation throughout the application.

```javascript
// components/layout/Layout.js
import React from 'react';
import { useLocation } from 'react-router-dom';
import Navigation from './Navigation';
import Footer from '../common/Footer';

const Layout = ({ children }) => {
  const location = useLocation();
  const isAuthPage = location.pathname === '/auth' || location.pathname === '/';

  return (
    <div className="min-h-screen flex flex-col">
      {!isAuthPage && <Navigation />}

      <main className={`flex-1 ${!isAuthPage ? 'pt-16' : ''}`}>
        {children}
      </main>

      {!isAuthPage && <Footer />}
    </div>
  );
};

export default Layout;
```

#### Reusable Component Patterns

##### 1. URL Input Form Component
```javascript
// components/analysis/UrlInputForm.js
import React, { useState } from 'react';
import { analysisService } from '../../services/api/analysisService';
import LoadingSpinner from '../common/Loading';
import ErrorMessage from '../common/ErrorMessage';

const UrlInputForm = ({ onSubmit }) => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Validate URL format
      const urlPattern = /^https?:\/\/.+/;
      if (!urlPattern.test(url)) {
        throw new Error('Please enter a valid URL starting with http:// or https://');
      }

      // Create analysis request
      const response = await analysisService.createAnalysis({
        url: url.trim(),
        settings: {
          includeAxeCore: true,
          wcagLevel: 'AA'
        }
      });

      // Call parent callback with analysis ID
      onSubmit(response.data.data.id);

      // Reset form
      setUrl('');
    } catch (error) {
      setError(error.response?.data?.message || error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full max-w-2xl mx-auto">
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="flex-1">
          <input
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="Enter website URL (e.g., https://example.com)"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
            disabled={loading}
          />
        </div>
        <button
          type="submit"
          disabled={loading || !url.trim()}
          className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          {loading ? <LoadingSpinner size="sm" /> : 'Analyze'}
        </button>
      </div>

      {error && (
        <ErrorMessage message={error} className="mt-3" />
      )}
    </form>
  );
};

export default UrlInputForm;
```

##### 2. Analysis Results Component
```javascript
// components/analysis/AnalysisResults.js
import React from 'react';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

const AnalysisResults = ({ results }) => {
  const { summary, violations, passes } = results;

  const getImpactColor = (impact) => {
    switch (impact) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'serious': return 'text-orange-600 bg-orange-50';
      case 'moderate': return 'text-yellow-600 bg-yellow-50';
      case 'minor': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getComplianceLevel = (score) => {
    if (score >= 90) return { level: 'Excellent', color: 'text-green-600' };
    if (score >= 75) return { level: 'Good', color: 'text-blue-600' };
    if (score >= 60) return { level: 'Fair', color: 'text-yellow-600' };
    return { level: 'Poor', color: 'text-red-600' };
  };

  const compliance = getComplianceLevel(summary.complianceScore);

  return (
    <div className="space-y-6">
      {/* Summary Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Accessibility Analysis Results
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className={`text-3xl font-bold ${compliance.color}`}>
              {summary.complianceScore}%
            </div>
            <div className="text-sm text-gray-600">Compliance Score</div>
            <div className={`text-sm font-medium ${compliance.color}`}>
              {compliance.level}
            </div>
          </div>

          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="text-3xl font-bold text-red-600">
              {summary.totalViolations}
            </div>
            <div className="text-sm text-gray-600">Issues Found</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-3xl font-bold text-green-600">
              {summary.totalPasses}
            </div>
            <div className="text-sm text-gray-600">Tests Passed</div>
          </div>
        </div>

        {/* Issue Breakdown */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="flex items-center space-x-2">
            <XCircleIcon className="h-5 w-5 text-red-500" />
            <span className="text-sm">
              <span className="font-medium">{summary.criticalIssues}</span> Critical
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />
            <span className="text-sm">
              <span className="font-medium">{summary.seriousIssues}</span> Serious
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
            <span className="text-sm">
              <span className="font-medium">{summary.moderateIssues}</span> Moderate
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="h-5 w-5 text-blue-500" />
            <span className="text-sm">
              <span className="font-medium">{summary.minorIssues}</span> Minor
            </span>
          </div>
        </div>
      </div>

      {/* Violations Section */}
      {violations.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            Accessibility Issues ({violations.length})
          </h3>

          <div className="space-y-4">
            {violations.map((violation, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">
                    {violation.help}
                  </h4>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getImpactColor(violation.impact)}`}>
                    {violation.impact}
                  </span>
                </div>

                <p className="text-sm text-gray-600 mb-3">
                  {violation.description}
                </p>

                <div className="text-xs text-gray-500">
                  <span className="font-medium">Affected elements:</span> {violation.nodes.length}
                </div>

                {violation.helpUrl && (
                  <a
                    href={violation.helpUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mt-2"
                  >
                    Learn more about this issue
                    <svg className="ml-1 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </a>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Passed Tests Section */}
      {passes.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            Passed Tests ({passes.length})
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {passes.slice(0, 10).map((pass, index) => (
              <div key={index} className="flex items-center space-x-2 p-2 bg-green-50 rounded">
                <CheckCircleIcon className="h-5 w-5 text-green-500 flex-shrink-0" />
                <span className="text-sm text-gray-700">{pass.help}</span>
              </div>
            ))}
          </div>

          {passes.length > 10 && (
            <div className="mt-3 text-sm text-gray-500">
              And {passes.length - 10} more passed tests...
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AnalysisResults;
```

### Service Layer Architecture

#### API Service Integration

The frontend uses a service layer pattern to abstract API communications and provide consistent error handling.

```javascript
// services/api/analysisService.js
import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: (process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000') + '/api',
  timeout: parseInt(process.env.REACT_APP_API_TIMEOUT) || 60000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      // Could redirect to login page
    }
    return Promise.reject(error);
  }
);

export const analysisService = {
  // Create analysis request
  createAnalysis: async (analysisData) => {
    const response = await api.post('/analysis', analysisData);
    return response;
  },

  // Get analysis with result extraction
  getAnalysis: async (analysisId) => {
    const response = await api.get(`/analysis/${analysisId}`);
    return { ...response, data: response.data.data };
  },

  // Poll for status updates
  pollAnalysisStatus: async (analysisId, onUpdate, maxAttempts = 60) => {
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await analysisService.getAnalysisStatus(analysisId);
        const statusData = response.data;

        const status = {
          status: statusData.analysisRequest?.status || statusData.status,
          message: statusData.scanStatus?.message || statusData.message,
          error: statusData.error
        };

        onUpdate(status);

        if ((status.status === 'processing' || status.status === 'pending') && attempts < maxAttempts) {
          attempts++;
          setTimeout(poll, 2000);
        }
      } catch (error) {
        onUpdate({ status: 'error', error: error.message });
      }
    };

    poll();
  }
};
```

#### Firebase Authentication Service

```javascript
// services/firebase/authService.js
import {
  getAuth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  updateProfile
} from 'firebase/auth';
import { initializeApp } from 'firebase/app';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

export const authService = {
  // Email/password sign up
  signUp: async (email, password, displayName) => {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);

    // Update profile with display name
    if (displayName) {
      await updateProfile(userCredential.user, { displayName });
    }

    return userCredential.user;
  },

  // Email/password sign in
  signIn: async (email, password) => {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  },

  // Google OAuth sign in
  signInWithGoogle: async () => {
    const userCredential = await signInWithPopup(auth, googleProvider);
    return userCredential.user;
  },

  // Sign out
  signOut: async () => {
    await signOut(auth);
  },

  // Auth state listener
  onAuthStateChanged: (callback) => {
    return onAuthStateChanged(auth, callback);
  },

  // Update user profile
  updateUserProfile: async (updates) => {
    const user = auth.currentUser;
    if (user) {
      await updateProfile(user, updates);
      return user;
    }
    throw new Error('No authenticated user');
  }
};
```

This completes the Frontend Deep Dive section, covering React architecture, component design patterns, state management, and service layer integration.

================================================================================
6. DATABASE DESIGN & FIRESTORE INTEGRATION
================================================================================

## Database Architecture Overview

The Accessibility Analyzer uses Firebase Firestore, a NoSQL document database that provides real-time synchronization, automatic scaling, and robust security features. The database design follows a document-oriented approach with collections optimized for the application's specific use cases.

### Database Design Principles

1. **Denormalization for Performance**: Data is structured to minimize read operations
2. **User-Centric Security**: All data is associated with users for proper access control
3. **Scalable Structure**: Collections designed to handle growth efficiently
4. **Real-time Capabilities**: Leverages Firestore's real-time listeners
5. **Offline Support**: Designed to work with Firestore's offline capabilities

## Collection Structure & Schema

### Users Collection (`users`)

```javascript
// Document ID: Firebase Auth UID
{
  uid: "firebase_auth_uid",
  email: "<EMAIL>",
  displayName: "John Doe",
  photoURL: "https://example.com/photo.jpg",
  createdAt: Timestamp,
  updatedAt: Timestamp,
  preferences: {
    theme: "light|dark",
    notifications: true,
    defaultWcagLevel: "AA",
    emailReports: false
  },
  subscription: {
    plan: "free|pro|enterprise",
    startDate: Timestamp,
    endDate: Timestamp,
    features: ["basic_scan", "advanced_analytics"]
  },
  usage: {
    totalScans: 0,
    monthlyScans: 0,
    lastScanDate: Timestamp,
    quotaUsed: 0,
    quotaLimit: 100
  }
}
```

**Purpose**: Stores user profile information, preferences, and usage statistics
**Access Control**: Users can only read/write their own documents
**Indexes**:
- Single field indexes on `email`, `createdAt`
- Composite index on `subscription.plan` + `createdAt`

### Analysis Requests Collection (`analysisRequests`)

```javascript
// Document ID: Auto-generated
{
  id: "auto_generated_id",
  url: "https://example.com",
  userId: "firebase_auth_uid", // null for anonymous users
  status: "pending|processing|completed|failed",
  requestTimestamp: Timestamp,
  completedTimestamp: Timestamp, // null until completed
  settings: {
    includeAxeCore: true,
    includeMlAnalysis: false,
    wcagLevel: "A|AA|AAA",
    includeScreenshot: true,
    waitTime: 5000,
    viewport: {
      width: 1280,
      height: 720
    }
  },
  metadata: {
    userAgent: "Mozilla/5.0...",
    ipAddress: "***********", // hashed for privacy
    referrer: "https://referrer.com",
    sessionId: "session_identifier",
    retryCount: 0,
    errorCategory: "network|timeout|csp|unknown",
    userFriendlyMessage: "Scan completed successfully"
  },
  priority: 1, // 1=high, 2=normal, 3=low
  estimatedDuration: 30, // seconds
  actualDuration: 25, // seconds
  createdAt: Timestamp,
  updatedAt: Timestamp
}
```

**Purpose**: Tracks accessibility analysis requests and their status
**Access Control**:
- Anyone can create (for anonymous users)
- Users can read their own requests
- Public requests (completed) are readable by all
**Indexes**:
- Single field: `userId`, `status`, `url`, `requestTimestamp`
- Composite: `userId` + `requestTimestamp`, `status` + `completedTimestamp`

### Analysis Results Collection (`analysisResults`)

```javascript
// Document ID: Auto-generated
{
  id: "auto_generated_id",
  analysisRequestId: "reference_to_analysis_request",
  url: "https://example.com",
  userId: "firebase_auth_uid", // null for anonymous users

  // Core axe-core results
  axeCoreResults: {
    url: "https://example.com",
    timestamp: "2024-01-15T10:30:00.000Z",
    violations: [
      {
        id: "color-contrast",
        impact: "serious",
        tags: ["wcag2aa", "wcag143"],
        description: "Elements must have sufficient color contrast",
        help: "Ensure all text elements have sufficient color contrast",
        helpUrl: "https://dequeuniversity.com/rules/axe/4.10/color-contrast",
        nodes: [
          {
            html: "<p class=\"low-contrast\">Text content</p>",
            target: [".low-contrast"],
            failureSummary: "Element has insufficient color contrast ratio",
            impact: "serious"
          }
        ]
      }
    ],
    passes: [
      {
        id: "document-title",
        impact: null,
        tags: ["wcag2a", "wcag242"],
        description: "Documents must have a title",
        help: "Page has an appropriate title",
        helpUrl: "https://dequeuniversity.com/rules/axe/4.10/document-title",
        nodeCount: 1
      }
    ],
    incomplete: [],
    inapplicable: []
  },

  // Processed summary data
  summary: {
    totalViolations: 15,
    totalPasses: 45,
    totalIncomplete: 3,
    totalInapplicable: 12,
    complianceScore: 75, // percentage
    wcagLevel: "AA",
    criticalIssues: 2,
    seriousIssues: 5,
    moderateIssues: 6,
    minorIssues: 2,
    affectedElements: 23,
    pageTitle: "Example Website",
    pageLanguage: "en",
    hasHeadingStructure: true,
    hasSkipLinks: false
  },

  // Categorized violations for better analysis
  violationsByCategory: {
    "Color & Contrast": 5,
    "Keyboard Navigation": 3,
    "Forms": 2,
    "Images & Media": 3,
    "Headings & Structure": 1,
    "Links": 1,
    "ARIA & Semantics": 0
  },

  // Recommendations for improvement
  recommendations: [
    {
      category: "Critical",
      title: "Fix Color Contrast Issues",
      description: "5 elements have insufficient color contrast ratios",
      impact: "Users with visual impairments may not be able to read content",
      solution: "Increase contrast ratios to meet WCAG AA standards (4.5:1 for normal text)",
      priority: 1,
      estimatedEffort: "2-4 hours",
      wcagReference: "1.4.3 Contrast (Minimum)"
    }
  ],

  // Page metadata
  pageMetadata: {
    title: "Example Website - Home Page",
    description: "Welcome to our example website",
    keywords: "example, website, demo",
    language: "en",
    charset: "UTF-8",
    viewport: "width=device-width, initial-scale=1",
    canonical: "https://example.com/",
    robots: "index, follow",
    loadTime: 2.5, // seconds
    pageSize: 1024000, // bytes
    resourceCount: 45
  },

  // Screenshot data (base64 encoded)
  screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",

  // Performance and technical data
  performance: {
    scanDuration: 25.5, // seconds
    browserVersion: "Chrome 120.0.0.0",
    axeVersion: "4.10.3",
    viewportSize: "1280x720",
    userAgent: "Mozilla/5.0...",
    javascriptEnabled: true,
    cookiesEnabled: true,
    networkCondition: "fast"
  },

  createdAt: Timestamp,
  updatedAt: Timestamp
}
```

**Purpose**: Stores detailed accessibility analysis results and processed data
**Access Control**: Same as analysis requests - user-owned or public completed results
**Indexes**:
- Single field: `analysisRequestId`, `userId`, `url`, `createdAt`
- Composite: `userId` + `createdAt`, `url` + `createdAt`

### User Preferences Collection (`userPreferences`)

```javascript
// Document ID: Firebase Auth UID
{
  userId: "firebase_auth_uid",
  dashboard: {
    defaultView: "analytics|history|recent",
    chartsEnabled: true,
    showTutorials: false,
    compactMode: false
  },
  scanning: {
    defaultWcagLevel: "AA",
    includeScreenshots: true,
    waitTime: 5000,
    retryFailedScans: true,
    notifyOnCompletion: true
  },
  notifications: {
    email: true,
    browser: false,
    scanComplete: true,
    weeklyReport: false,
    securityAlerts: true
  },
  privacy: {
    shareResults: false,
    allowAnalytics: true,
    dataRetention: 90 // days
  },
  createdAt: Timestamp,
  updatedAt: Timestamp
}
```

**Purpose**: Stores user-specific preferences and settings
**Access Control**: Users can only access their own preferences

## Security Rules Implementation

### Firestore Security Rules (firestore.rules)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }

    function isValidAnalysisRequest() {
      return request.resource.data.keys().hasAll(['url', 'status', 'requestTimestamp']) &&
             request.resource.data.url is string &&
             request.resource.data.url.size() > 0 &&
             request.resource.data.status in ['pending', 'processing', 'completed', 'failed'];
    }

    // Users collection - users can only access their own data
    match /users/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Analysis Requests collection
    match /analysisRequests/{requestId} {
      // Allow anyone to create analysis requests (for anonymous users)
      allow create: if isValidAnalysisRequest();

      // Allow reading if:
      // - The request is public (no userId) OR
      // - The user owns the request OR
      // - The request is completed (for public viewing)
      allow read: if resource.data.userId == null
                  || (isAuthenticated() && isOwner(resource.data.userId))
                  || resource.data.status == 'completed';

      // Allow updating only if user owns the request
      allow update: if isAuthenticated() && isOwner(resource.data.userId);

      // Allow deleting only if user owns the request
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Analysis Results collection
    match /analysisResults/{resultId} {
      // Only allow creating results through backend (server-side)
      allow create: if false;

      // Allow reading with same logic as analysis requests
      allow read: if resource.data.userId == null
                  || (isAuthenticated() && isOwner(resource.data.userId))
                  || exists(/databases/$(database)/documents/analysisRequests/$(resource.data.analysisRequestId))
                     && get(/databases/$(database)/documents/analysisRequests/$(resource.data.analysisRequestId)).data.status == 'completed';

      // Only allow updating through backend
      allow update: if false;

      // Allow deleting only if user owns the result
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // User preferences
    match /userPreferences/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Future collections for scalability
    match /apiKeys/{keyId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
    }

    match /analytics/{analyticsId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only backend can write analytics
    }

    match /publicStats/{statId} {
      allow read: if true;
      allow write: if false; // Only backend can write stats
    }
  }
}
```

### Security Features

1. **User Isolation**: Users can only access their own data
2. **Anonymous Support**: Allows anonymous users to create analysis requests
3. **Public Results**: Completed analyses can be viewed publicly for sharing
4. **Backend-Only Operations**: Critical operations restricted to server-side code
5. **Data Validation**: Ensures data structure integrity
6. **Granular Permissions**: Different permissions for different operations

## Database Indexes & Performance

### Firestore Indexes (firestore.indexes.json)

```json
{
  "indexes": [
    {
      "collectionGroup": "analysisRequests",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "userId", "order": "ASCENDING" },
        { "fieldPath": "requestTimestamp", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "analysisRequests",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "status", "order": "ASCENDING" },
        { "fieldPath": "completedTimestamp", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "analysisResults",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "userId", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "analysisResults",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "url", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    }
  ],
  "fieldOverrides": []
}
```

### Performance Optimization Strategies

1. **Composite Indexes**: Optimized for common query patterns
2. **Pagination Support**: Cursor-based pagination for large datasets
3. **Selective Field Retrieval**: Only fetch required fields
4. **Caching Strategy**: Client-side caching for frequently accessed data
5. **Batch Operations**: Group related operations for efficiency

## Data Flow & Lifecycle

### Analysis Request Lifecycle

```
1. User submits URL for analysis
   ↓
2. Frontend creates analysisRequest document (status: 'pending')
   ↓
3. Backend picks up request and updates status to 'processing'
   ↓
4. Scanning service performs accessibility analysis
   ↓
5. Results are processed and analysisResult document is created
   ↓
6. analysisRequest status updated to 'completed'
   ↓
7. Frontend polls for completion and displays results
```

### Data Consistency Patterns

1. **Eventual Consistency**: Accepts temporary inconsistency for performance
2. **Compensating Actions**: Handles failed operations with cleanup
3. **Idempotent Operations**: Safe to retry operations
4. **Optimistic Updates**: Updates UI immediately, handles conflicts later

This completes the Database Design & Firestore Integration section, covering the complete data architecture, security implementation, and performance optimization strategies.

================================================================================
7. AUTHENTICATION SYSTEM
================================================================================

## Authentication Architecture Overview

The Accessibility Analyzer implements a comprehensive authentication system using Firebase Authentication, providing secure user management with multiple sign-in methods, session management, and seamless integration between frontend and backend components.

### Authentication Flow Architecture

```
Frontend (React) ←→ Firebase Auth ←→ Backend (Express)
     ↓                    ↓                ↓
User Interface    Token Management    Token Verification
State Management  Session Handling    User Authorization
Route Protection  OAuth Integration   API Security
```

### Key Authentication Features

1. **Multiple Sign-in Methods**: Email/password and Google OAuth
2. **Anonymous User Support**: Allows usage without registration
3. **Secure Token Management**: JWT tokens with automatic refresh
4. **Session Persistence**: Maintains login state across browser sessions
5. **Route Protection**: Secures authenticated-only pages
6. **Real-time State Updates**: Instant authentication state changes

## Firebase Authentication Configuration

### Frontend Firebase Setup

```javascript
// services/firebase/authService.js
import {
  getAuth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  updateProfile,
  sendPasswordResetEmail
} from 'firebase/auth';
import { initializeApp } from 'firebase/app';

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

// Configure Google OAuth provider
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

export const authService = {
  // Email/password registration
  signUp: async (email, password, displayName) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);

      // Update profile with display name
      if (displayName) {
        await updateProfile(userCredential.user, { displayName });
      }

      return userCredential.user;
    } catch (error) {
      throw new Error(getAuthErrorMessage(error));
    }
  },

  // Email/password sign in
  signIn: async (email, password) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (error) {
      throw new Error(getAuthErrorMessage(error));
    }
  },

  // Google OAuth sign in
  signInWithGoogle: async () => {
    try {
      const userCredential = await signInWithPopup(auth, googleProvider);
      return userCredential.user;
    } catch (error) {
      if (error.code === 'auth/popup-closed-by-user') {
        throw new Error('Sign-in popup was closed before completing.');
      }
      throw new Error(getAuthErrorMessage(error));
    }
  },

  // Password reset
  resetPassword: async (email) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      throw new Error(getAuthErrorMessage(error));
    }
  },

  // Sign out
  signOut: async () => {
    try {
      await signOut(auth);
    } catch (error) {
      throw new Error('Failed to sign out');
    }
  },

  // Auth state listener
  onAuthStateChanged: (callback) => {
    return onAuthStateChanged(auth, callback);
  },

  // Update user profile
  updateUserProfile: async (updates) => {
    try {
      const user = auth.currentUser;
      if (user) {
        await updateProfile(user, updates);
        return user;
      }
      throw new Error('No authenticated user');
    } catch (error) {
      throw new Error('Failed to update profile');
    }
  },

  // Get current user
  getCurrentUser: () => {
    return auth.currentUser;
  },

  // Get ID token
  getIdToken: async () => {
    const user = auth.currentUser;
    if (user) {
      return await user.getIdToken();
    }
    return null;
  }
};

// Error message mapping
const getAuthErrorMessage = (error) => {
  switch (error.code) {
    case 'auth/user-not-found':
      return 'No account found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters.';
    case 'auth/invalid-email':
      return 'Invalid email address.';
    case 'auth/popup-blocked':
      return 'Sign-in popup was blocked by the browser.';
    case 'auth/cancelled-popup-request':
      return 'Only one popup request is allowed at a time.';
    default:
      return error.message || 'An error occurred during authentication.';
  }
};
```

### Backend Firebase Admin Setup

```javascript
// config/firebase-admin.js
const admin = require('firebase-admin');
const path = require('path');

let db = null;
let auth = null;

const initializeFirebase = () => {
  try {
    // Check if already initialized
    if (admin.apps.length > 0) {
      console.log('✅ Firebase Admin already initialized');
      db = admin.firestore();
      auth = admin.auth();
      return;
    }

    // Service Account Key Path
    const serviceAccountPath = path.join(__dirname, 'firebase-service-account-key.json');

    // Initialize with service account
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccountPath),
      projectId: process.env.FIREBASE_PROJECT_ID
    });

    // Initialize services
    db = admin.firestore();
    auth = admin.auth();

    // Configure Firestore settings
    db.settings({
      timestampsInSnapshots: true,
      ignoreUndefinedProperties: true
    });

    console.log('✅ Firebase Admin SDK initialized successfully');
  } catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    throw error;
  }
};

const getFirestore = () => {
  if (!db) {
    throw new Error('Firestore not initialized. Call initializeFirebase() first.');
  }
  return db;
};

const getAuth = () => {
  if (!auth) {
    throw new Error('Auth not initialized. Call initializeFirebase() first.');
  }
  return auth;
};

module.exports = {
  initializeFirebase,
  getFirestore,
  getAuth,
  admin
};
```

## Authentication Context & State Management

### React Authentication Context

```javascript
// contexts/AuthContext.js
import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '../services/firebase/authService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    // Listen for auth state changes
    const unsubscribe = authService.onAuthStateChanged(async (user) => {
      try {
        setUser(user);
        setLoading(false);

        // Store auth token in localStorage for API requests
        if (user) {
          const token = await user.getIdToken();
          localStorage.setItem('authToken', token);

          // Set up token refresh
          setupTokenRefresh(user);
        } else {
          localStorage.removeItem('authToken');
        }
      } catch (error) {
        console.error('Error handling auth state change:', error);
        setError('Authentication error occurred');
      } finally {
        setInitializing(false);
      }
    });

    return unsubscribe;
  }, []);

  // Token refresh mechanism
  const setupTokenRefresh = (user) => {
    // Refresh token every 50 minutes (tokens expire after 1 hour)
    const refreshInterval = setInterval(async () => {
      try {
        if (user) {
          const token = await user.getIdToken(true); // Force refresh
          localStorage.setItem('authToken', token);
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
        clearInterval(refreshInterval);
      }
    }, 50 * 60 * 1000); // 50 minutes

    // Clear interval when user signs out
    return () => clearInterval(refreshInterval);
  };

  const signUp = async (email, password, displayName) => {
    try {
      setError('');
      setLoading(true);
      const user = await authService.signUp(email, password, displayName);
      return user;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email, password) => {
    try {
      setError('');
      setLoading(true);
      const user = await authService.signIn(email, password);
      return user;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    try {
      setError('');
      setLoading(true);
      const user = await authService.signInWithGoogle();
      return user;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setError('');
      await authService.signOut();
      localStorage.removeItem('authToken');
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  const updateProfile = async (updates) => {
    try {
      setError('');
      const updatedUser = await authService.updateUserProfile(updates);
      setUser(updatedUser);
      return updatedUser;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  const resetPassword = async (email) => {
    try {
      setError('');
      await authService.resetPassword(email);
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  const value = {
    user,
    loading,
    error,
    initializing,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    updateProfile,
    resetPassword,
    isAuthenticated: !!user,
    displayName: user?.displayName || user?.email?.split('@')[0] || 'User',
    email: user?.email,
    photoURL: user?.photoURL,
    uid: user?.uid
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
```

## Backend Authentication Middleware

### Token Verification Middleware

```javascript
// middleware/auth.js
const { getAuth } = require('../config/firebase-admin');

// Verify Firebase ID token
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'No valid authorization token provided'
      });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the token with Firebase Admin
    const decodedToken = await getAuth().verifyIdToken(token);

    // Add user info to request object
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      name: decodedToken.name,
      picture: decodedToken.picture,
      firebase: decodedToken
    };

    next();
  } catch (error) {
    console.error('Token verification failed:', error);

    if (error.code === 'auth/id-token-expired') {
      return res.status(401).json({
        error: 'Token Expired',
        message: 'Authentication token has expired'
      });
    }

    if (error.code === 'auth/id-token-revoked') {
      return res.status(401).json({
        error: 'Token Revoked',
        message: 'Authentication token has been revoked'
      });
    }

    return res.status(401).json({
      error: 'Invalid Token',
      message: 'Authentication token is invalid'
    });
  }
};

// Optional authentication (for endpoints that work with or without auth)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await getAuth().verifyIdToken(token);

      req.user = {
        uid: decodedToken.uid,
        email: decodedToken.email,
        emailVerified: decodedToken.email_verified,
        name: decodedToken.name,
        picture: decodedToken.picture,
        firebase: decodedToken
      };
    }

    next();
  } catch (error) {
    // Continue without authentication for optional auth
    console.warn('Optional auth failed:', error.message);
    next();
  }
};

// Admin role verification
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    // Check custom claims for admin role
    const userRecord = await getAuth().getUser(req.user.uid);
    const customClaims = userRecord.customClaims || {};

    if (!customClaims.admin) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Admin privileges required'
      });
    }

    next();
  } catch (error) {
    console.error('Admin verification failed:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to verify admin privileges'
    });
  }
};

module.exports = {
  verifyToken,
  optionalAuth,
  requireAdmin
};
```

## Route Protection & Navigation Guards

### Protected Route Component

```javascript
// components/common/ProtectedRoute.js
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Loading from './Loading';

const ProtectedRoute = ({ children, requireAuth = true, redirectTo = '/' }) => {
  const { user, loading, initializing } = useAuth();
  const location = useLocation();

  // Show loading while authentication state is being determined
  if (loading || initializing) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" message="Checking authentication..." />
      </div>
    );
  }

  // Redirect unauthenticated users
  if (requireAuth && !user) {
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location.pathname }}
        replace
      />
    );
  }

  // Redirect authenticated users from auth pages
  if (!requireAuth && user) {
    const from = location.state?.from || '/home';
    return <Navigate to={from} replace />;
  }

  return children;
};

export default ProtectedRoute;
```

### Navigation with Authentication State

```javascript
// components/layout/Navigation.js
import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  UserIcon,
  ChartBarIcon,
  HomeIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';

const Navigation = () => {
  const { user, signOut, isAuthenticated, displayName } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/home" className="flex items-center space-x-2">
            <span className="text-xl font-bold text-blue-600">
              Accessibility Analyzer
            </span>
          </Link>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-6">
            <Link
              to="/home"
              className="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors"
            >
              <HomeIcon className="h-5 w-5" />
              <span>Home</span>
            </Link>

            {isAuthenticated && (
              <>
                <Link
                  to="/dashboard"
                  className="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors"
                >
                  <ChartBarIcon className="h-5 w-5" />
                  <span>Dashboard</span>
                </Link>

                <Link
                  to="/profile"
                  className="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors"
                >
                  <UserIcon className="h-5 w-5" />
                  <span>Profile</span>
                </Link>
              </>
            )}
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-600">
                  Welcome, {displayName}
                </span>
                <button
                  onClick={handleSignOut}
                  className="flex items-center space-x-1 text-gray-600 hover:text-red-600 transition-colors"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5" />
                  <span>Sign Out</span>
                </button>
              </div>
            ) : (
              <Link
                to="/"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Sign In
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
```

This completes the Authentication System section, covering Firebase integration, token management, state management, middleware implementation, and route protection strategies.
