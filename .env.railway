# Railway Environment Variables Template
# Copy these to Railway dashboard under Variables tab

# Application Configuration
NODE_ENV=production
PORT=3000

# Railway Configuration (Railway will auto-populate RAILWAY_STATIC_URL)
FRONTEND_URL=${{RAILWAY_STATIC_URL}}
PUBLIC_IP=${{RAILWAY_STATIC_URL}}

# Firebase Configuration (REPLACE WITH YOUR ACTUAL VALUES)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=your-service-account-email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key-here\n-----END PRIVATE KEY-----"
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_CLIENT_CERT_URL=your-client-cert-url

# Playwright Configuration
PLAYWRIGHT_BROWSERS_PATH=/app/node_modules/playwright
PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false

# Security
HELMET_CSP_DISABLED=true

# Logging
LOG_LEVEL=info
